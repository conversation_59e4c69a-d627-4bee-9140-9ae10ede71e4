"""
Search Agent V2 - Intelligent sub-agent with multiple search tools
"""

import logging
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.tools import tool
from langchain_core.prompts import <PERSON><PERSON><PERSON><PERSON>pt<PERSON><PERSON>plate
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver
import os
from dotenv import load_dotenv
from config import get_vector_store_manager
from typing import Dict, List, Any

load_dotenv()
logger = logging.getLogger(__name__)


class SearchAgentV2:
    """Intelligent search sub-agent with multiple tools for dynamic search decisions"""

    def __init__(self, tenant_id: str = "ambition-guru"):
        """Initialize with vector store, LLM, and sub-agent tools"""
        self.tenant_id = tenant_id
        self.vector_manager = get_vector_store_manager(tenant_id)
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        self.memory = MemorySaver()
        self._setup_search_sub_agent()
        logger.info("✅ Search Agent V2 initialized with intelligent sub-agent")

    def _setup_search_sub_agent(self):
        """Setup the intelligent search sub-agent with multiple tools"""

        # Create search tools for the sub-agent
        @tool
        def search_information_tool(query: str) -> str:
            """
            Search for general information, troubleshooting, apps, and technical issues.
            Use this for: app problems, how-to guides, troubleshooting, general information.

            Args:
                query: Search query for information

            Returns:
                Information search results
            """
            logger.info(f"🔍 Sub-agent using information search: '{query}'")
            try:
                result = self.vector_manager.search_information(query)
                if "currently unavailable" in result.lower() or "having trouble" in result.lower():
                    return self._llm_information_search(query)
                return result
            except Exception as e:
                logger.warning(f"Information search failed: {e}")
                return self._llm_information_search(query)

        @tool
        def search_products_tool(query: str) -> str:
            """
            Search for products, courses, and educational programs.
            Use this for: courses, programs, what's available, educational offerings, course catalogs.

            Args:
                query: Search query for products/courses

            Returns:
                Product/course search results with codes from metadata
            """
            logger.info(f"🎓 Sub-agent using products search: '{query}'")
            try:
                result = self.vector_manager.search_products(query)
                if "currently unavailable" in result.lower() or "having trouble" in result.lower():
                    return self._llm_products_search(query)
                return result
            except Exception as e:
                logger.warning(f"Products search failed: {e}")
                return self._llm_products_search(query)

        # Store tools for the sub-agent
        self.search_tools = [search_information_tool, search_products_tool]

        # Create the intelligent search sub-agent
        search_agent_prompt = """You are an intelligent search assistant. You have access to two search tools:

1. search_information_tool: For troubleshooting, technical issues, app problems, how-to guides, general information
2. search_products_tool: For courses, programs, educational offerings, what's available

DECISION RULES:
- If the user asks about courses, programs, education, or "what do you have" → use search_products_tool
- If the user asks about troubleshooting, apps, technical issues, or general info → use search_information_tool
- If the query could benefit from BOTH types of information → use BOTH tools
- If the query is ambiguous → use BOTH tools to provide comprehensive results

EXAMPLES:
- "what courses do you have?" → use search_products_tool only
- "app not working" → use search_information_tool only
- "I want to learn programming, what do you offer and how do I start?" → use BOTH tools
- "tell me about Python" → use BOTH tools (could be asking about courses AND general info)

Always provide helpful, comprehensive responses. When using both tools, clearly organize the information.
Return the tool call details in your response so the user knows what searches were performed."""

        self.search_sub_agent = create_react_agent(
            model=self.llm,
            tools=self.search_tools,
            checkpointer=self.memory,
            prompt=ChatPromptTemplate.from_messages([
                ("system", search_agent_prompt),
                ("placeholder", "{messages}"),
            ])
        )

    def intelligent_search(self, user_message: str, thread_id: str = "default") -> Dict[str, Any]:
        """
        Use the intelligent search sub-agent to determine and execute appropriate searches

        Returns:
            Dict with 'response', 'tools_used', and 'search_details'
        """
        logger.info(f"🤖 Sub-agent search: '{user_message}'")

        # Configure thread for memory persistence
        config = {"configurable": {"thread_id": thread_id}}

        try:
            # Invoke the search sub-agent
            response = self.search_sub_agent.invoke(
                {"messages": [HumanMessage(content=user_message)]},
                config=config
            )

            # Extract the final response
            final_response = response["messages"][-1].content

            # Extract tool usage information
            tools_used = []
            search_details = {
                'sub_agent_used': True,
                'tools_executed': []
            }

            for msg in response["messages"]:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_name = tool_call['name']
                        tool_args = tool_call.get('args', {})

                        tools_used.append({
                            'name': tool_name,
                            'description': f"Sub-agent used: {tool_name}",
                            'input': tool_args,
                            'output': f"Tool executed by sub-agent"
                        })

                        search_details['tools_executed'].append({
                            'tool': tool_name,
                            'query': tool_args.get('query', user_message),
                            'status': 'success'
                        })

            if tools_used:
                logger.info(f"🔧 Sub-agent used tools: {[tool['name'] for tool in tools_used]}")
            else:
                logger.warning("⚠️ Sub-agent didn't use any tools")

            return {
                'response': final_response,
                'tools_used': tools_used,
                'search_details': search_details
            }

        except Exception as e:
            error_msg = f"Error in sub-agent search: {str(e)}"
            logger.error(error_msg)
            return {
                'response': error_msg,
                'tools_used': [],
                'search_details': {'error': error_msg}
            }

    def search_information(self, user_message: str, thread_id: str = "default") -> str:
        """
        Dynamic information search using vector store with LLM fallback
        """
        logger.info(f"🔍 Information search: '{user_message}'")

        try:
            # First try vector store search
            result = self.vector_manager.search_information(user_message)

            # If vector store returns generic message, use LLM fallback
            if "currently unavailable" in result.lower() or "having trouble" in result.lower():
                logger.info("📋 Vector store unavailable, using LLM fallback")
                return self._llm_information_search(user_message)

            logger.info(f"✅ Information search completed via vector store")
            return result

        except Exception as e:
            logger.warning(f"Vector store search failed: {e}, using LLM fallback")
            return self._llm_information_search(user_message)

    def _llm_information_search(self, user_message: str) -> str:
        """LLM fallback for information search"""
        system_prompt = """You are a helpful customer service agent for an educational service center in Nepal.

The user is asking for general information, troubleshooting help, or guidance.

Provide helpful, accurate information about:
- Educational services and support
- Technical troubleshooting for apps and online platforms
- General guidance about studying in Nepal
- Information about educational systems (SEE, +2, Bachelor's, etc.)
- Study abroad guidance
- Language learning tips

Be helpful, professional, and provide practical advice. If you don't know something specific, suggest they contact support for detailed help.
"""

        try:
            response = self.llm.invoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"User question: {user_message}")
            ])

            return response.content

        except Exception as e:
            error_msg = f"Error in information search: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble accessing information right now. Please try again or contact our support team for assistance."

    def search_products(self, user_message: str, thread_id: str = "default") -> str:
        """
        Dynamic product/course search using vector store with LLM fallback
        """
        logger.info(f"🎓 Products search: '{user_message}'")

        try:
            # First try vector store search
            result = self.vector_manager.search_products(user_message)

            # If vector store returns generic message, use LLM fallback
            if "currently unavailable" in result.lower() or "having trouble" in result.lower():
                logger.info("🎓 Vector store unavailable, using LLM fallback")
                return self._llm_products_search(user_message)

            logger.info(f"✅ Products search completed via vector store")
            return result

        except Exception as e:
            logger.warning(f"Vector store search failed: {e}, using LLM fallback")
            return self._llm_products_search(user_message)

    def _llm_products_search(self, user_message: str) -> str:
        """LLM fallback for products search"""
        system_prompt = """You are a course advisor for an educational service center in Nepal.

The user is asking about courses, programs, or educational services.

Based on their query, provide information about relevant educational programs such as:
- SEE Bridge courses (Grade 10 to +2 transition)
- Bachelor's programs (BBS, BBA, CSIT, etc.)
- Language courses (IELTS, Korean TOPIK, German)
- Entrance exam preparation
- Professional development courses

Guidelines:
1. If they ask about a specific course that exists in Nepal's education system, provide helpful information
2. If they ask about something that doesn't exist or isn't offered, be honest and suggest similar alternatives
3. Always be encouraging and helpful
4. Suggest they contact us for detailed course information, schedules, and enrollment

DO NOT make up specific course codes, prices, or schedules. Focus on general information and encourage them to contact for details.
"""

        try:
            response = self.llm.invoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"User inquiry: {user_message}")
            ])

            return response.content

        except Exception as e:
            error_msg = f"Error in products search: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble accessing course information right now. Please try again or contact our support team for detailed course information."


# Global instance
_search_agent_instance = None

def get_search_agent() -> SearchAgentV2:
    """Get or create the global search agent instance"""
    global _search_agent_instance
    if _search_agent_instance is None:
        _search_agent_instance = SearchAgentV2()
    return _search_agent_instance
