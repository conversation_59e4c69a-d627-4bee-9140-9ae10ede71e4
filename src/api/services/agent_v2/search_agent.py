"""
Search Agent V2 - Intelligent search using vector store with dynamic tool selection
"""

import logging
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import ChatPromptTemplate
import os
from dotenv import load_dotenv
from config import get_vector_store_manager
from typing import Dict, List, Any

load_dotenv()
logger = logging.getLogger(__name__)


class SearchAgentV2:
    """Intelligent search agent with dynamic tool selection and detailed response tracking"""

    def __init__(self, tenant_id: str = "ambition-guru"):
        """Initialize with vector store and LLM"""
        self.tenant_id = tenant_id
        self.vector_manager = get_vector_store_manager(tenant_id)
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.3,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        logger.info("✅ Search Agent V2 initialized with intelligent tool selection")

    def analyze_search_intent(self, user_message: str) -> Dict[str, Any]:
        """
        Analyze user message to determine which search tools to use

        Returns:
            Dict with 'tools_to_use', 'reasoning', and 'search_queries'
        """
        system_prompt = """You are a search intent analyzer. Analyze the user's message and determine which search tools should be used.

Available tools:
1. search_information: For troubleshooting, technical issues, general information, app problems, how-to guides
2. search_products: For courses, programs, educational offerings, course catalogs, what's available

Rules for tool selection:
- Use ONLY search_products if asking about courses, programs, what's available, educational offerings
- Use ONLY search_information if asking about troubleshooting, technical issues, app problems, general info
- Use BOTH tools if the query could benefit from both types of information (e.g., "I want to learn programming, what courses do you have and how do I get started?")
- Use BOTH tools if the query is ambiguous and could relate to either

Respond in this exact format:
TOOLS: [list of tools to use: "search_information", "search_products", or both]
REASONING: [brief explanation of why these tools were selected]
QUERIES: [optimized search queries for each tool, separated by " | " if multiple]

Examples:
- "what courses do you have?" → TOOLS: ["search_products"] REASONING: Asking about available courses QUERIES: what courses available
- "app not working" → TOOLS: ["search_information"] REASONING: Technical troubleshooting issue QUERIES: app not working troubleshooting
- "I want to learn Python, what do you offer?" → TOOLS: ["search_products", "search_information"] REASONING: Needs both course info and learning guidance QUERIES: Python courses programming | Python learning guide programming basics"""

        try:
            response = self.llm.invoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"Analyze this query: {user_message}")
            ])

            content = response.content.strip()

            # Parse the response
            tools_to_use = []
            reasoning = ""
            search_queries = []

            for line in content.split('\n'):
                if line.startswith('TOOLS:'):
                    tools_str = line.replace('TOOLS:', '').strip()
                    # Extract tools from the string representation of list
                    if 'search_information' in tools_str:
                        tools_to_use.append('search_information')
                    if 'search_products' in tools_str:
                        tools_to_use.append('search_products')
                elif line.startswith('REASONING:'):
                    reasoning = line.replace('REASONING:', '').strip()
                elif line.startswith('QUERIES:'):
                    queries_str = line.replace('QUERIES:', '').strip()
                    search_queries = [q.strip() for q in queries_str.split('|')]

            # Fallback if parsing fails
            if not tools_to_use:
                if any(word in user_message.lower() for word in ['course', 'program', 'class', 'study', 'learn', 'education']):
                    tools_to_use = ['search_products']
                    search_queries = [user_message]
                else:
                    tools_to_use = ['search_information']
                    search_queries = [user_message]
                reasoning = "Fallback analysis based on keywords"

            logger.info(f"🧠 Intent analysis: {tools_to_use} - {reasoning}")

            return {
                'tools_to_use': tools_to_use,
                'reasoning': reasoning,
                'search_queries': search_queries
            }

        except Exception as e:
            logger.error(f"Error in intent analysis: {e}")
            # Default fallback
            return {
                'tools_to_use': ['search_information'],
                'reasoning': 'Error in analysis, defaulting to information search',
                'search_queries': [user_message]
            }

    def intelligent_search(self, user_message: str, thread_id: str = "default") -> Dict[str, Any]:
        """
        Perform intelligent search using dynamic tool selection

        Returns:
            Dict with 'response', 'tools_used', and 'search_details'
        """
        logger.info(f"🔍 Intelligent search: '{user_message}'")

        # Analyze intent to determine which tools to use
        intent_analysis = self.analyze_search_intent(user_message)
        tools_to_use = intent_analysis['tools_to_use']
        search_queries = intent_analysis['search_queries']

        results = []
        tools_used = []
        search_details = {
            'intent_analysis': intent_analysis,
            'tools_executed': []
        }

        # Execute the determined tools
        for i, tool_name in enumerate(tools_to_use):
            query = search_queries[i] if i < len(search_queries) else user_message

            try:
                if tool_name == 'search_information':
                    result = self.search_information(query, thread_id)
                    tool_detail = {
                        'tool': 'search_information',
                        'query': query,
                        'result_preview': result[:200] + "..." if len(result) > 200 else result,
                        'status': 'success'
                    }
                elif tool_name == 'search_products':
                    result = self.search_products(query, thread_id)
                    tool_detail = {
                        'tool': 'search_products',
                        'query': query,
                        'result_preview': result[:200] + "..." if len(result) > 200 else result,
                        'status': 'success'
                    }
                else:
                    continue

                results.append(result)
                tools_used.append({
                    'name': tool_name,
                    'description': f"Searched for: {query}",
                    'input': {'query': query},
                    'output': result
                })
                search_details['tools_executed'].append(tool_detail)

            except Exception as e:
                error_msg = f"Error in {tool_name}: {str(e)}"
                logger.error(error_msg)
                search_details['tools_executed'].append({
                    'tool': tool_name,
                    'query': query,
                    'error': error_msg,
                    'status': 'error'
                })

        # Combine results if multiple tools were used
        if len(results) > 1:
            combined_response = f"Here's what I found:\n\n" + "\n\n---\n\n".join(results)
        elif len(results) == 1:
            combined_response = results[0]
        else:
            combined_response = "I apologize, but I couldn't find relevant information for your query. Please try rephrasing or contact support."

        logger.info(f"✅ Intelligent search completed using {len(tools_to_use)} tools")

        return {
            'response': combined_response,
            'tools_used': tools_used,
            'search_details': search_details
        }

    def search_information(self, user_message: str, thread_id: str = "default") -> str:
        """
        Dynamic information search using vector store with LLM fallback
        """
        logger.info(f"🔍 Information search: '{user_message}'")

        try:
            # First try vector store search
            result = self.vector_manager.search_information(user_message)

            # If vector store returns generic message, use LLM fallback
            if "currently unavailable" in result.lower() or "having trouble" in result.lower():
                logger.info("📋 Vector store unavailable, using LLM fallback")
                return self._llm_information_search(user_message)

            logger.info(f"✅ Information search completed via vector store")
            return result

        except Exception as e:
            logger.warning(f"Vector store search failed: {e}, using LLM fallback")
            return self._llm_information_search(user_message)

    def _llm_information_search(self, user_message: str) -> str:
        """LLM fallback for information search"""
        system_prompt = """You are a helpful customer service agent for an educational service center in Nepal.

The user is asking for general information, troubleshooting help, or guidance.

Provide helpful, accurate information about:
- Educational services and support
- Technical troubleshooting for apps and online platforms
- General guidance about studying in Nepal
- Information about educational systems (SEE, +2, Bachelor's, etc.)
- Study abroad guidance
- Language learning tips

Be helpful, professional, and provide practical advice. If you don't know something specific, suggest they contact support for detailed help.
"""

        try:
            response = self.llm.invoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"User question: {user_message}")
            ])

            return response.content

        except Exception as e:
            error_msg = f"Error in information search: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble accessing information right now. Please try again or contact our support team for assistance."

    def search_products(self, user_message: str, thread_id: str = "default") -> str:
        """
        Dynamic product/course search using vector store with LLM fallback
        """
        logger.info(f"🎓 Products search: '{user_message}'")

        try:
            # First try vector store search
            result = self.vector_manager.search_products(user_message)

            # If vector store returns generic message, use LLM fallback
            if "currently unavailable" in result.lower() or "having trouble" in result.lower():
                logger.info("🎓 Vector store unavailable, using LLM fallback")
                return self._llm_products_search(user_message)

            logger.info(f"✅ Products search completed via vector store")
            return result

        except Exception as e:
            logger.warning(f"Vector store search failed: {e}, using LLM fallback")
            return self._llm_products_search(user_message)

    def _llm_products_search(self, user_message: str) -> str:
        """LLM fallback for products search"""
        system_prompt = """You are a course advisor for an educational service center in Nepal.

The user is asking about courses, programs, or educational services.

Based on their query, provide information about relevant educational programs such as:
- SEE Bridge courses (Grade 10 to +2 transition)
- Bachelor's programs (BBS, BBA, CSIT, etc.)
- Language courses (IELTS, Korean TOPIK, German)
- Entrance exam preparation
- Professional development courses

Guidelines:
1. If they ask about a specific course that exists in Nepal's education system, provide helpful information
2. If they ask about something that doesn't exist or isn't offered, be honest and suggest similar alternatives
3. Always be encouraging and helpful
4. Suggest they contact us for detailed course information, schedules, and enrollment

DO NOT make up specific course codes, prices, or schedules. Focus on general information and encourage them to contact for details.
"""

        try:
            response = self.llm.invoke([
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"User inquiry: {user_message}")
            ])

            return response.content

        except Exception as e:
            error_msg = f"Error in products search: {str(e)}"
            logger.error(error_msg)
            return "I apologize, but I'm having trouble accessing course information right now. Please try again or contact our support team for detailed course information."


# Global instance
_search_agent_instance = None

def get_search_agent() -> SearchAgentV2:
    """Get or create the global search agent instance"""
    global _search_agent_instance
    if _search_agent_instance is None:
        _search_agent_instance = SearchAgentV2()
    return _search_agent_instance
